// AUTOMATICALLY GENERATED FILE. PLEASE DO NOT MODIFY IT MANUALLY
//
// PlatformIO Debugging Solution
//
// Documentation: https://docs.platformio.org/en/latest/plus/debugging.html
// Configuration: https://docs.platformio.org/en/latest/projectconf/sections/env/options/debug/index.html

{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug",
            "executable": "c:/Users/<USER>/Downloads/rr/rr/.pio/build/uno_r4_wifi/firmware.elf",
            "projectEnvName": "uno_r4_wifi",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-gccarmnoneeabi/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "svdPath": "C:/Users/<USER>/.platformio/platforms/renesas-ra/misc/svd/R7FA4M1AB.svd",
            "preLaunchTask": {
                "type": "PlatformIO",
                "task": "Pre-Debug"
            }
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (skip Pre-Debug)",
            "executable": "c:/Users/<USER>/Downloads/rr/rr/.pio/build/uno_r4_wifi/firmware.elf",
            "projectEnvName": "uno_r4_wifi",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-gccarmnoneeabi/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "svdPath": "C:/Users/<USER>/.platformio/platforms/renesas-ra/misc/svd/R7FA4M1AB.svd"
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (without uploading)",
            "executable": "c:/Users/<USER>/Downloads/rr/rr/.pio/build/uno_r4_wifi/firmware.elf",
            "projectEnvName": "uno_r4_wifi",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-gccarmnoneeabi/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "svdPath": "C:/Users/<USER>/.platformio/platforms/renesas-ra/misc/svd/R7FA4M1AB.svd",
            "loadMode": "manual"
        }
    ]
}
