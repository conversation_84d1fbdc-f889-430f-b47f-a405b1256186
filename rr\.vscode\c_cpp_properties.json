//
// !!! WARNING !!! AUTO-GENERATED FILE!
// PLEASE DO NOT MODIFY IT AND USE "platformio.ini":
// https://docs.platformio.org/page/projectconf/section_env_build.html#build-flags
//
{
    "configurations": [
        {
            "name": "PlatformIO",
            "includePath": [
                "c:/Users/<USER>/Downloads/rr/rr/include",
                "c:/Users/<USER>/Downloads/rr/rr/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/EEPROM/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Storage",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/BlockDevices",
                "c:/Users/<USER>/Downloads/rr/rr/.pio/libdeps/uno_r4_wifi/Stepper/src",
                "c:/Users/<USER>/Downloads/rr/rr/.pio/libdeps/uno_r4_wifi/Servo/src",
                "c:/Users/<USER>/Downloads/rr/rr/.pio/libdeps/uno_r4_wifi/aWOT/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/WiFiS3/src",
                "c:/Users/<USER>/Downloads/rr/rr/.pio/libdeps/uno_r4_wifi/ArduinoOTA/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/cores/arduino/tinyusb",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/cores/arduino",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/cores/arduino/api/deprecated",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/cores/arduino/api/deprecated-avr-comp",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/variants/UNOWIFIR4",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/AnalogWave",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Arduino_CAN/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Arduino_FreeRTOS/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Arduino_LED_Matrix/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/FATFilesystem",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/HID",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/I2S",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/LittleFilesystem",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/OPAMP/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/OTAUpdate/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Preferences/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/RTC/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/SDU/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/SPI",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/SoftwareATSE/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/SoftwareSerial/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/WDT/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Wire",
                ""
            ],
            "browse": {
                "limitSymbolsToIncludedHeaders": true,
                "path": [
                    "c:/Users/<USER>/Downloads/rr/rr/include",
                    "c:/Users/<USER>/Downloads/rr/rr/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/EEPROM/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Storage",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/BlockDevices",
                    "c:/Users/<USER>/Downloads/rr/rr/.pio/libdeps/uno_r4_wifi/Stepper/src",
                    "c:/Users/<USER>/Downloads/rr/rr/.pio/libdeps/uno_r4_wifi/Servo/src",
                    "c:/Users/<USER>/Downloads/rr/rr/.pio/libdeps/uno_r4_wifi/aWOT/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/WiFiS3/src",
                    "c:/Users/<USER>/Downloads/rr/rr/.pio/libdeps/uno_r4_wifi/ArduinoOTA/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/cores/arduino/tinyusb",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/cores/arduino",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/cores/arduino/api/deprecated",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/cores/arduino/api/deprecated-avr-comp",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/variants/UNOWIFIR4",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/AnalogWave",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Arduino_CAN/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Arduino_FreeRTOS/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Arduino_LED_Matrix/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/FATFilesystem",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/HID",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/I2S",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/LittleFilesystem",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/OPAMP/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/OTAUpdate/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Preferences/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/RTC/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/SDU/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/SPI",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/SoftwareATSE/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/SoftwareSerial/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/WDT/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/libraries/Wire",
                    ""
                ]
            },
            "defines": [
                "PLATFORMIO=60118",
                "ARDUINO_UNOWIFIR4",
                "ARDUINO_ARCH_RENESAS_UNO",
                "ARDUINO_UNOR4_WIFI",
                "NO_USB",
                "BACKTRACE_SUPPORT",
                "LWIP_DNS=1",
                "CFG_TUSB_MCU=OPT_MCU_RAXXX",
                "_RA_CORE=CM4",
                "_RENESAS_RA_",
                "ARDUINO=10810",
                "ARDUINO_ARCH_RENESAS",
                "ARDUINO_FSP",
                "_XOPEN_SOURCE=700",
                "F_CPU=48000000L",
                ""
            ],
            "cStandard": "gnu11",
            "cppStandard": "gnu++17",
            "compilerPath": "C:/Users/<USER>/.platformio/packages/toolchain-gccarmnoneeabi/bin/arm-none-eabi-gcc.exe",
            "compilerArgs": [
                "-mcpu=cortex-m4",
                "-mfloat-abi=hard",
                "-mfpu=fpv4-sp-d16",
                "-mthumb",
                "-iprefixC:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno",
                "@C:/Users/<USER>/.platformio/packages/framework-arduinorenesas-uno/variants/UNOWIFIR4/includes.txt",
                ""
            ]
        }
    ],
    "version": 4
}
