import time
import serial.tools.list_ports
import os

from SCons.Script import ARGUMENTS, DefaultEnvironment

env = DefaultEnvironment()
platform = env.PioPlatform()

upload_port_before = [p.device for p in serial.tools.list_ports.comports()]

def before_upload(source, target, env):
    print("🔄 Resetting board via 1200bps...")
    port = env.subst("$UPLOAD_PORT")
    if not port:
        raise Exception("No UPLOAD_PORT defined!")

    # Open port at 1200bps to trigger reset
    ser = serial.Serial(port, 1200)
    ser.close()

    # Wait for the board to disconnect and reconnect
    time.sleep(0.5)

    print("🧭 Waiting for new port...")
    for i in range(20):
        current_ports = [p.device for p in serial.tools.list_ports.comports()]
        new_ports = list(set(current_ports) - set(upload_port_before))
        if new_ports:
            new_port = new_ports[0]
            print(f"✅ Found new port: {new_port}")
            env["UPLOAD_PORT"] = new_port
            return
        time.sleep(0.25)

    raise Exception("❌ Timeout: No new port found after 1200bps reset.")

env.Replace(
    UPLOADER="python",
    UPLOADERFLAGS=["-m", "platformio", "run", "-t", "upload"],
)

env.AddPreAction("upload", before_upload)
