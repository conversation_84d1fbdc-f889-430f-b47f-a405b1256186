#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arduino Serial Controller - KivyMD Modern GUI
Modern Arduino Control Interface with Kivy<PERSON>
"""

import os
import sys
import time
import json
import threading
import queue
from datetime import datetime
from kivy.app import App
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.core.window import Window
from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.slider import MDSlider

from kivymd.uix.selectioncontrol import MDCheckbox, MDSwitch
from kivymd.uix.list import MDList, OneLineListItem, TwoLineListItem
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.navigationdrawer import MDNavigationDrawer, MDNavigationDrawerMenu
from kivymd.uix.tab import MDTabs, MDTabsBase
from kivymd.uix.floatlayout import MDFloatLayout
from kivymd.uix.anchorlayout import MDAnchorLayout
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar import MDSnackbar
from kivymd.uix.spinner import MDSpinner
from kivymd.color_definitions import colors
from kivymd.theming import ThemableBehavior

try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False

class ArduinoControllerApp(MDApp):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Arduino Controller Pro"
        self.theme_cls.primary_palette = "Teal"
        self.theme_cls.accent_palette = "Orange"
        self.theme_cls.theme_style = "Dark"  # Start with dark theme
        
        # Serial connection
        self.serial_connection = None
        self.is_connected = False
        self.auto_refresh = False

        # Threading for serial communication
        self.serial_thread = None
        self.serial_queue = queue.Queue()
        self.response_queue = queue.Queue()
        self.serial_running = False
        self.response_event = None
        
        # Data storage
        self.pwm_values = [0, 0, 0]
        self.stepper_data = {"angle": 0.0, "speed": 12, "mode": "IDLE"}
        self.relay_data = {
            "right": {"active": False, "timer": 5, "remaining": 0},
            "left": {"active": False, "timer": 5, "remaining": 0}
        }
        self.shoot_rate = 1
        self.continuous_shooting = False
        
        # Settings file
        self.settings_file = "arduino_kivymd_settings.json"
        self.load_settings()
        
        # Auto refresh event
        self.refresh_event = None
        
    def build(self):
        """Build the main application"""
        # Set window properties for responsiveness
        Window.minimum_width = dp(320)
        Window.minimum_height = dp(480)
        
        # Create main screen manager
        self.screen_manager = MDScreenManager()
        
        # Create main screen
        main_screen = MDScreen(name="main")
        main_screen.add_widget(self.create_main_layout())
        self.screen_manager.add_widget(main_screen)
        
        # Start auto refresh
        self.start_auto_refresh()
        
        return self.screen_manager
    
    def create_main_layout(self):
        """Create the main application layout"""
        # Main container
        main_layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Top app bar
        toolbar = MDTopAppBar(
            title="Arduino Controller Pro",
            elevation=3,
            left_action_items=[["menu", lambda x: self.toggle_theme()]],
            right_action_items=[
                ["theme-light-dark", lambda x: self.toggle_theme()],
                ["refresh", lambda x: self.manual_refresh()],
                ["fullscreen", lambda x: self.toggle_fullscreen()]
            ]
        )
        main_layout.add_widget(toolbar)
        
        # Connection status card
        connection_card = self.create_connection_card()
        main_layout.add_widget(connection_card)
        
        # Tabs for different controls
        tabs = self.create_control_tabs()
        main_layout.add_widget(tabs)
        
        # Log area
        log_card = self.create_log_card()
        main_layout.add_widget(log_card)
        
        return main_layout
    
    def create_connection_card(self):
        """Create connection status and control card"""
        card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(180),
            elevation=3,
            padding=dp(15),
            spacing=dp(10)
        )
        
        # Status indicator
        status_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(40))
        
        self.connection_status_label = MDLabel(
            text="Disconnected",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_x=0.7
        )
        status_layout.add_widget(self.connection_status_label)
        
        # Connection spinner
        self.connection_spinner = MDSpinner(
            size_hint=(None, None),
            size=(dp(30), dp(30)),
            active=False
        )
        status_layout.add_widget(self.connection_spinner)
        
        card.add_widget(status_layout)
        
        # Port selection
        port_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(50), spacing=dp(10))
        
        self.port_field = MDTextField(
            hint_text="Select Port",
            mode="rectangle",
            size_hint_x=0.5
        )
        port_layout.add_widget(self.port_field)
        
        self.baud_field = MDTextField(
            hint_text="Baud Rate",
            text="115200",
            mode="rectangle",
            size_hint_x=0.3
        )
        port_layout.add_widget(self.baud_field)
        
        refresh_btn = MDIconButton(
            icon="refresh",
            theme_icon_color="Primary",
            on_release=self.refresh_ports
        )
        port_layout.add_widget(refresh_btn)
        
        card.add_widget(port_layout)
        
        # Connection buttons
        button_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(50), spacing=dp(10))
        
        self.connect_btn = MDRaisedButton(
            text="Connect",
            md_bg_color=self.theme_cls.primary_color,
            on_release=self.toggle_connection
        )
        button_layout.add_widget(self.connect_btn)
        
        card.add_widget(button_layout)
        
        # Refresh ports on startup
        Clock.schedule_once(lambda dt: self.refresh_ports(), 0.5)
        
        return card
    
    def create_control_tabs(self):
        """Create control sections in a scrollable layout"""
        scroll = MDScrollView()
        main_content = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        main_content.bind(minimum_height=main_content.setter('height'))

        # PWM Control Section
        pwm_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(50),
            elevation=2,
            padding=dp(10)
        )
        pwm_title = MDLabel(
            text="PWM Control",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(30)
        )
        pwm_card.add_widget(pwm_title)
        main_content.add_widget(pwm_card)
        main_content.add_widget(self.create_pwm_content())

        # Shooting Control Section
        shoot_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(50),
            elevation=2,
            padding=dp(10)
        )
        shoot_title = MDLabel(
            text="Pulse Control",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(30)
        )
        shoot_card.add_widget(shoot_title)
        main_content.add_widget(shoot_card)
        main_content.add_widget(self.create_shooting_content())

        # Stepper Motor Section
        stepper_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(50),
            elevation=2,
            padding=dp(10)
        )
        stepper_title = MDLabel(
            text="Stepper Motor",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(30)
        )
        stepper_card.add_widget(stepper_title)
        main_content.add_widget(stepper_card)
        main_content.add_widget(self.create_stepper_content())

        # Relay Control Section
        relay_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(50),
            elevation=2,
            padding=dp(10)
        )
        relay_title = MDLabel(
            text="Relay Control",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(30)
        )
        relay_card.add_widget(relay_title)
        main_content.add_widget(relay_card)
        main_content.add_widget(self.create_relay_content())

        # Settings Section
        settings_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(50),
            elevation=2,
            padding=dp(10)
        )
        settings_title = MDLabel(
            text="Settings",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(30)
        )
        settings_card.add_widget(settings_title)
        main_content.add_widget(settings_card)
        main_content.add_widget(self.create_settings_content())

        scroll.add_widget(main_content)
        return scroll
    
    def create_pwm_content(self):
        """Create PWM control content"""
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), padding=dp(15))
        content.bind(minimum_height=content.setter('height'))
        
        # PWM channels
        self.pwm_sliders = []
        self.pwm_labels = []
        
        channels = [
            ("Red Channel (D9)", "red"),
            ("Blue Channel (D6)", "blue"),
            ("Green Channel (D5)", "green")
        ]
        
        for i, (name, color) in enumerate(channels):
            # Channel card
            card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height=dp(200),
                elevation=2,
                padding=dp(15),
                spacing=dp(10)
            )
            
            # Channel title
            title = MDLabel(
                text=name,
                theme_text_color="Primary",
                font_style="H6",
                size_hint_y=None,
                height=dp(30)
            )
            card.add_widget(title)
            
            # Value display
            value_label = MDLabel(
                text="0 (0.00V)",
                theme_text_color="Secondary",
                font_style="Body1",
                size_hint_y=None,
                height=dp(30)
            )
            self.pwm_labels.append(value_label)
            card.add_widget(value_label)
            
            # Slider
            slider = MDSlider(
                min=0,
                max=255,
                value=self.pwm_values[i],
                size_hint_y=None,
                height=dp(50)
            )
            slider.bind(value=lambda instance, value, ch=i: self.on_pwm_change(ch, value))
            self.pwm_sliders.append(slider)
            card.add_widget(slider)
            
            # Quick buttons
            button_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(40), spacing=dp(5))
            
            presets = [("0%", 0), ("25%", 64), ("50%", 128), ("75%", 192), ("100%", 255)]
            for label, value in presets:
                btn = MDFlatButton(
                    text=label,
                    on_release=lambda x, ch=i, val=value: self.set_pwm_value(ch, val)
                )
                button_layout.add_widget(btn)
            
            card.add_widget(button_layout)
            content.add_widget(card)
        
        scroll.add_widget(content)
        return scroll

    def create_shooting_content(self):
        """Create shooting control content"""
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), padding=dp(15))
        content.bind(minimum_height=content.setter('height'))

        # Single shot card
        single_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(150),
            elevation=2,
            padding=dp(15),
            spacing=dp(10)
        )

        single_title = MDLabel(
            text="Single Pulse",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        single_card.add_widget(single_title)

        self.pulse_btn = MDRaisedButton(
            text="Send Pulse",
            md_bg_color=self.theme_cls.primary_color,
            size_hint_y=None,
            height=dp(60),
            on_release=self.single_shoot
        )
        single_card.add_widget(self.pulse_btn)

        content.add_widget(single_card)

        # Continuous shooting card
        cont_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(250),
            elevation=2,
            padding=dp(15),
            spacing=dp(10)
        )

        cont_title = MDLabel(
            text="Continuous Pulses",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        cont_card.add_widget(cont_title)

        # Rate control
        rate_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(50))

        rate_label = MDLabel(
            text="Pulse Rate:",
            theme_text_color="Secondary",
            size_hint_x=0.4
        )
        rate_layout.add_widget(rate_label)

        self.rate_slider = MDSlider(
            min=0,
            max=10,
            value=self.shoot_rate,
            size_hint_x=0.6
        )
        self.rate_slider.bind(value=self.on_rate_change)
        rate_layout.add_widget(self.rate_slider)

        cont_card.add_widget(rate_layout)

        self.rate_value_label = MDLabel(
            text=f"{self.shoot_rate} Hz",
            theme_text_color="Primary",
            font_style="Body1",
            size_hint_y=None,
            height=dp(30)
        )
        cont_card.add_widget(self.rate_value_label)

        # Control buttons
        button_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(50), spacing=dp(10))

        self.start_shoot_btn = MDRaisedButton(
            text="Start",
            md_bg_color=self.theme_cls.primary_color,
            on_release=self.start_continuous_shooting
        )
        button_layout.add_widget(self.start_shoot_btn)

        self.stop_shoot_btn = MDRaisedButton(
            text="Stop",
            md_bg_color=self.theme_cls.error_color,
            on_release=self.stop_continuous_shooting
        )
        button_layout.add_widget(self.stop_shoot_btn)

        cont_card.add_widget(button_layout)

        # Status
        self.shoot_status_label = MDLabel(
            text="Status: Stopped",
            theme_text_color="Secondary",
            font_style="Body1",
            size_hint_y=None,
            height=dp(30)
        )
        cont_card.add_widget(self.shoot_status_label)

        content.add_widget(cont_card)
        scroll.add_widget(content)
        return scroll

    def create_stepper_content(self):
        """Create stepper motor control content"""
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), padding=dp(15))
        content.bind(minimum_height=content.setter('height'))

        # Status card
        status_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(100),
            elevation=2,
            padding=dp(15)
        )

        status_title = MDLabel(
            text="Motor Status",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        status_card.add_widget(status_title)

        self.stepper_status_label = MDLabel(
            text="Angle: 0.0° | Speed: 12 RPM | Mode: IDLE",
            theme_text_color="Secondary",
            font_style="Body1",
            size_hint_y=None,
            height=dp(40)
        )
        status_card.add_widget(self.stepper_status_label)

        content.add_widget(status_card)

        # Angle control card
        angle_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(200),
            elevation=2,
            padding=dp(15),
            spacing=dp(10)
        )

        angle_title = MDLabel(
            text="Angle Control",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        angle_card.add_widget(angle_title)

        self.angle_field = MDTextField(
            hint_text="Target Angle (0-359)",
            mode="rectangle",
            input_filter="int",
            size_hint_y=None,
            height=dp(50)
        )
        angle_card.add_widget(self.angle_field)

        goto_btn = MDRaisedButton(
            text="Go to Angle",
            md_bg_color=self.theme_cls.primary_color,
            size_hint_y=None,
            height=dp(40),
            on_release=self.goto_angle
        )
        angle_card.add_widget(goto_btn)

        # Speed control
        speed_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(50))

        speed_label = MDLabel(
            text="Speed:",
            theme_text_color="Secondary",
            size_hint_x=0.3
        )
        speed_layout.add_widget(speed_label)

        self.speed_slider = MDSlider(
            min=1,
            max=20,
            value=self.stepper_data["speed"],
            size_hint_x=0.7
        )
        self.speed_slider.bind(value=self.on_speed_change)
        speed_layout.add_widget(self.speed_slider)

        angle_card.add_widget(speed_layout)

        self.speed_value_label = MDLabel(
            text=f"{self.stepper_data['speed']} RPM",
            theme_text_color="Primary",
            font_style="Body1",
            size_hint_y=None,
            height=dp(30)
        )
        angle_card.add_widget(self.speed_value_label)

        content.add_widget(angle_card)

        # Movement control card
        move_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(250),
            elevation=2,
            padding=dp(15),
            spacing=dp(10)
        )

        move_title = MDLabel(
            text="Movement Control",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        move_card.add_widget(move_title)

        cw_btn = MDRaisedButton(
            text="Clockwise",
            md_bg_color=self.theme_cls.primary_color,
            size_hint_y=None,
            height=dp(40),
            on_release=self.stepper_cw
        )
        move_card.add_widget(cw_btn)

        ccw_btn = MDRaisedButton(
            text="Counter-Clockwise",
            md_bg_color=self.theme_cls.primary_color,
            size_hint_y=None,
            height=dp(40),
            on_release=self.stepper_ccw
        )
        move_card.add_widget(ccw_btn)

        stop_btn = MDRaisedButton(
            text="Stop Movement",
            md_bg_color=self.theme_cls.error_color,
            size_hint_y=None,
            height=dp(40),
            on_release=self.stepper_stop
        )
        move_card.add_widget(stop_btn)

        reset_btn = MDRaisedButton(
            text="Reset Position",
            md_bg_color=self.theme_cls.accent_color,
            size_hint_y=None,
            height=dp(40),
            on_release=self.stepper_reset
        )
        move_card.add_widget(reset_btn)

        content.add_widget(move_card)
        scroll.add_widget(content)
        return scroll

    def create_relay_content(self):
        """Create relay control content"""
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), padding=dp(15))
        content.bind(minimum_height=content.setter('height'))

        # Right relay card
        right_card = self.create_relay_card("RIGHT", "Right Relay (D10)")
        content.add_widget(right_card)

        # Left relay card
        left_card = self.create_relay_card("LEFT", "Left Relay (D11)")
        content.add_widget(left_card)

        scroll.add_widget(content)
        return scroll

    def create_relay_card(self, relay_id, title):
        """Create individual relay control card"""
        card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(220),
            elevation=2,
            padding=dp(15),
            spacing=dp(10)
        )

        # Title
        title_label = MDLabel(
            text=title,
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        card.add_widget(title_label)

        # Timer setting
        timer_field = MDTextField(
            hint_text="Timer (seconds, 0 for manual)",
            text=str(self.relay_data[relay_id.lower()]["timer"]),
            mode="rectangle",
            input_filter="int",
            size_hint_y=None,
            height=dp(50)
        )
        setattr(self, f"timer_{relay_id.lower()}_field", timer_field)
        card.add_widget(timer_field)

        # Control buttons
        button_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(50), spacing=dp(10))

        on_btn = MDRaisedButton(
            text="Turn On",
            md_bg_color=self.theme_cls.primary_color,
            on_release=lambda x: self.relay_on(relay_id)
        )
        button_layout.add_widget(on_btn)

        off_btn = MDRaisedButton(
            text="Turn Off",
            md_bg_color=self.theme_cls.error_color,
            on_release=lambda x: self.relay_off(relay_id)
        )
        button_layout.add_widget(off_btn)

        card.add_widget(button_layout)

        # Status display
        status_label = MDLabel(
            text="Status: Off",
            theme_text_color="Secondary",
            font_style="Body1",
            size_hint_y=None,
            height=dp(40)
        )
        setattr(self, f"relay_{relay_id.lower()}_status", status_label)
        card.add_widget(status_label)

        return card

    def create_settings_content(self):
        """Create settings content"""
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), padding=dp(15))
        content.bind(minimum_height=content.setter('height'))

        # Auto refresh card
        auto_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(150),
            elevation=2,
            padding=dp(15),
            spacing=dp(10)
        )

        auto_title = MDLabel(
            text="Auto Refresh",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        auto_card.add_widget(auto_title)

        # Auto refresh switch
        switch_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(50))

        switch_label = MDLabel(
            text="Auto refresh status every 2 seconds",
            theme_text_color="Secondary",
            size_hint_x=0.8
        )
        switch_layout.add_widget(switch_label)

        self.auto_refresh_switch = MDSwitch(
            active=self.auto_refresh,
            size_hint_x=0.2
        )
        self.auto_refresh_switch.bind(active=self.toggle_auto_refresh)
        switch_layout.add_widget(self.auto_refresh_switch)

        auto_card.add_widget(switch_layout)

        manual_refresh_btn = MDRaisedButton(
            text="Manual Refresh",
            md_bg_color=self.theme_cls.primary_color,
            size_hint_y=None,
            height=dp(40),
            on_release=lambda x: self.manual_refresh()
        )
        auto_card.add_widget(manual_refresh_btn)

        content.add_widget(auto_card)

        # Save/Load settings card
        save_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(150),
            elevation=2,
            padding=dp(15),
            spacing=dp(10)
        )

        save_title = MDLabel(
            text="Save Settings",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        save_card.add_widget(save_title)

        save_btn = MDRaisedButton(
            text="Save to Arduino",
            md_bg_color=self.theme_cls.primary_color,
            size_hint_y=None,
            height=dp(40),
            on_release=self.save_arduino_settings
        )
        save_card.add_widget(save_btn)

        reset_btn = MDRaisedButton(
            text="Reset Arduino to Defaults",
            md_bg_color=self.theme_cls.error_color,
            size_hint_y=None,
            height=dp(40),
            on_release=self.reset_arduino_settings
        )
        save_card.add_widget(reset_btn)

        content.add_widget(save_card)

        # Theme card
        theme_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(120),
            elevation=2,
            padding=dp(15),
            spacing=dp(10)
        )

        theme_title = MDLabel(
            text="Theme",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30)
        )
        theme_card.add_widget(theme_title)

        theme_btn = MDRaisedButton(
            text="Toggle Dark/Light Mode",
            md_bg_color=self.theme_cls.accent_color,
            size_hint_y=None,
            height=dp(40),
            on_release=lambda x: self.toggle_theme()
        )
        theme_card.add_widget(theme_btn)

        content.add_widget(theme_card)
        scroll.add_widget(content)
        return scroll

    def create_log_card(self):
        """Create log display card"""
        card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(200),
            elevation=3,
            padding=dp(10)
        )

        # Log header
        header_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(40))

        log_title = MDLabel(
            text="Event Log",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_x=0.8
        )
        header_layout.add_widget(log_title)

        clear_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Primary",
            on_release=self.clear_log
        )
        header_layout.add_widget(clear_btn)

        card.add_widget(header_layout)

        # Log content
        self.log_scroll = MDScrollView()
        self.log_list = MDList()
        self.log_scroll.add_widget(self.log_list)
        card.add_widget(self.log_scroll)

        # Add initial log entry
        self.log("Arduino Modern Interface Started", "success")

        return card

    # Utility Functions
    def log(self, message, log_type="info"):
        """Add message to log with color coding"""
        timestamp = time.strftime("%H:%M:%S")

        # Color coding based on type
        if log_type == "success":
            icon = "[SUCCESS]"
            color = "green"
        elif log_type == "error":
            icon = "[ERROR]"
            color = "red"
        elif log_type == "warning":
            icon = "[WARNING]"
            color = "orange"
        else:
            icon = "[INFO]"
            color = "blue"

        log_text = f"[{timestamp}] {icon} {message}"

        # Add to log list
        log_item = TwoLineListItem(
            text=log_text,
            secondary_text=f"Type: {log_type.upper()}",
            theme_text_color="Primary"
        )

        self.log_list.add_widget(log_item)

        # Auto scroll to bottom
        Clock.schedule_once(lambda dt: self.scroll_to_bottom(), 0.1)

        # Keep only last 100 entries
        if len(self.log_list.children) > 100:
            self.log_list.remove_widget(self.log_list.children[-1])

    def scroll_to_bottom(self):
        """Scroll log to bottom"""
        if hasattr(self, 'log_scroll'):
            self.log_scroll.scroll_y = 0

    def clear_log(self, *args):
        """Clear log entries"""
        self.log_list.clear_widgets()
        self.log("Log cleared", "info")

    def show_snackbar(self, message):
        """Show snackbar notification"""
        MDSnackbar(MDLabel(text=message), y=dp(24), pos_hint={"center_x": 0.5}).open()

    def show_dialog(self, title, message):
        """Show dialog"""
        dialog = MDDialog(
            title=title,
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    # Connection Functions
    def refresh_ports(self, *args):
        """Refresh available serial ports"""
        if not SERIAL_AVAILABLE:
            self.show_snackbar("❌ PySerial not available")
            return

        try:
            ports = serial.tools.list_ports.comports()
            port_list = [port.device for port in ports]

            if port_list:
                self.port_field.text = port_list[0]  # Select first port
                self.log(f"Found {len(port_list)} port(s)", "info")
            else:
                self.log("No ports found", "warning")

        except Exception as e:
            self.log(f"Error searching for ports: {str(e)}", "error")

    def toggle_connection(self, *args):
        """Toggle serial connection"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self, *args):
        """Connect to Arduino"""
        if not SERIAL_AVAILABLE:
            self.show_snackbar("❌ PySerial not installed")
            return

        try:
            port = self.port_field.text.strip()
            baud = int(self.baud_field.text.strip() or "115200")

            if not port:
                self.show_snackbar("Please select a port")
                return

            self.connection_spinner.active = True
            self.log(f"Attempting to connect to {port} at {baud} baud...", "info")

            # Connect in thread to avoid blocking UI
            threading.Thread(target=self._connect_thread, args=(port, baud), daemon=True).start()

        except ValueError:
            self.show_snackbar("Invalid baud rate")
        except Exception as e:
            self.log(f"Connection failed: {str(e)}", "error")
            self.connection_spinner.active = False

    def _connect_thread(self, port, baud):
        """Connection thread"""
        try:
            self.serial_connection = serial.Serial(port, baud, timeout=1)
            time.sleep(2)  # Wait for Arduino to initialize

            # Update UI on main thread
            Clock.schedule_once(lambda dt: self._connection_success(port), 0)

        except Exception as e:
            Clock.schedule_once(lambda dt: self._connection_failed(str(e)), 0)

    def _connection_success(self, port):
        """Handle successful connection"""
        self.is_connected = True
        self.connection_spinner.active = False
        self.connection_status_label.text = "Connected"
        self.connect_btn.text = "Disconnect"
        self.log(f"Successfully connected to {port}", "success")
        self.show_snackbar("Connected successfully")

        # Start serial communication thread
        self.serial_running = True
        self.serial_thread = threading.Thread(target=self._serial_thread_worker, daemon=True)
        self.serial_thread.start()

        # Start response processing
        self.response_event = Clock.schedule_interval(self._process_serial_responses, 0.1)

        # Get initial status
        self.manual_refresh()

    def _connection_failed(self, error):
        """Handle connection failure"""
        self.connection_spinner.active = False
        self.log(f"Connection failed: {error}", "error")
        self.show_snackbar(f"Connection failed: {error}")

    def _serial_thread_worker(self):
        """Serial communication thread worker"""
        while self.serial_running and self.serial_connection:
            try:
                # Check for commands to send
                try:
                    command = self.serial_queue.get(timeout=0.1)
                    if command is None:  # Shutdown signal
                        break

                    # Send command
                    self.serial_connection.write((command + '\n').encode())
                    time.sleep(0.1)  # Small delay for Arduino to process

                    # Wait for response
                    response = ""
                    start_time = time.time()
                    while time.time() - start_time < 2:  # 2 second timeout
                        if self.serial_connection.in_waiting:
                            response = self.serial_connection.readline().decode().strip()
                            break
                        time.sleep(0.01)

                    # Put response in queue for main thread
                    self.response_queue.put((command, response))

                except queue.Empty:
                    continue
                except Exception as e:
                    self.response_queue.put((None, f"Error: {str(e)}"))

            except Exception as e:
                self.response_queue.put((None, f"Thread error: {str(e)}"))
                break

    def _process_serial_responses(self, dt):
        """Process serial responses in main thread"""
        try:
            while True:
                command, response = self.response_queue.get_nowait()

                if command is None:  # Error message
                    self.log(response, "error")
                else:
                    # Color-coded logging based on response
                    if response:
                        if "ERROR" in response:
                            self.log(f"TX: {command} -> RX: {response}", "error")
                        elif "OK" in response or "COMPLETE" in response or "STARTED" in response or "STOPPED" in response:
                            self.log(f"TX: {command} -> RX: {response}", "success")
                        else:
                            self.log(f"TX: {command} -> RX: {response}", "info")
                    else:
                        self.log(f"TX: {command} -> No response", "warning")

        except queue.Empty:
            pass

    def disconnect(self, *args):
        """Disconnect from Arduino"""
        # Stop serial thread
        self.serial_running = False
        if self.serial_thread and self.serial_thread.is_alive():
            self.serial_queue.put(None)  # Signal thread to stop
            self.serial_thread.join(timeout=1)

        # Stop response processing
        if self.response_event:
            self.response_event.cancel()
            self.response_event = None

        if self.serial_connection:
            self.serial_connection.close()
            self.serial_connection = None

        self.is_connected = False
        self.connection_status_label.text = "Disconnected"
        self.connect_btn.text = "Connect"
        self.log("Disconnected", "warning")
        self.show_snackbar("Disconnected")

    def send_command(self, command):
        """Send command to Arduino using threaded communication"""
        if not self.is_connected or not self.serial_connection or not self.serial_running:
            self.log(f"Error: Not connected - {command}", "warning")
            return None

        try:
            # Put command in queue for serial thread to process
            self.serial_queue.put(command)
            return True  # Command queued successfully

        except Exception as e:
            self.log(f"Send error: {str(e)}", "error")
            return None

    # PWM Functions
    def on_pwm_change(self, channel, value):
        """Handle PWM slider change"""
        value = int(value)
        self.pwm_values[channel] = value

        # Update label
        voltage = (value / 255.0) * 5.0
        self.pwm_labels[channel].text = f"{value} ({voltage:.2f}V)"

        # Send command
        self.send_command(f"SET_PWM,{channel},{value}")

    def set_pwm_value(self, channel, value):
        """Set PWM value directly"""
        self.pwm_sliders[channel].value = value
        self.on_pwm_change(channel, value)

    # Shooting Functions
    def single_shoot(self, *args):
        """Send single pulse"""
        self.send_command("SINGLE_SHOOT")
        self.show_snackbar("Single pulse sent")

    def on_rate_change(self, instance, value):
        """Handle rate slider change"""
        self.shoot_rate = int(value)
        self.rate_value_label.text = f"{self.shoot_rate} Hz"
        self.send_command(f"SET_RATE,{self.shoot_rate}")

    def start_continuous_shooting(self, *args):
        """Start continuous shooting"""
        self.send_command("START_SHOOT")
        self.continuous_shooting = True
        self.shoot_status_label.text = "Status: Running"
        self.show_snackbar("Continuous pulses started")

    def stop_continuous_shooting(self, *args):
        """Stop continuous shooting"""
        self.send_command("STOP_SHOOT")
        self.continuous_shooting = False
        self.shoot_status_label.text = "Status: Stopped"
        self.show_snackbar("Continuous pulses stopped")

    # Stepper Functions
    def goto_angle(self, *args):
        """Move stepper to specific angle"""
        try:
            angle = int(self.angle_field.text or "0")
            if 0 <= angle < 360:
                self.send_command(f"STEPPER_ANGLE,{angle}")
                self.show_snackbar(f"Moving to angle {angle}°")
            else:
                self.show_snackbar("Angle must be between 0 and 359")
        except ValueError:
            self.show_snackbar("Please enter a valid number")

    def on_speed_change(self, instance, value):
        """Handle speed slider change"""
        speed = int(value)
        self.stepper_data["speed"] = speed
        self.speed_value_label.text = f"{speed} RPM"
        self.send_command(f"STEPPER_SPEED,{speed}")

    def stepper_cw(self, *args):
        """Start clockwise rotation"""
        self.send_command("STEPPER_CW")
        self.show_snackbar("Clockwise rotation")

    def stepper_ccw(self, *args):
        """Start counter-clockwise rotation"""
        self.send_command("STEPPER_CCW")
        self.show_snackbar("Counter-clockwise rotation")

    def stepper_stop(self, *args):
        """Stop stepper motor"""
        self.send_command("STEPPER_STOP")
        self.show_snackbar("Motor stopped")

    def stepper_reset(self, *args):
        """Reset stepper position"""
        self.send_command("STEPPER_RESET")
        self.show_snackbar("Position reset")

    # Relay Functions
    def relay_on(self, relay_id):
        """Turn relay on"""
        timer_field = getattr(self, f"timer_{relay_id.lower()}_field")
        timer = int(timer_field.text or "0")
        self.send_command(f"RELAY_{relay_id}_ON,{timer}")
        self.show_snackbar(f"Relay {relay_id} turned on")

    def relay_off(self, relay_id):
        """Turn relay off"""
        self.send_command(f"RELAY_{relay_id}_OFF")
        self.show_snackbar(f"Relay {relay_id} turned off")

    # Settings Functions
    def toggle_auto_refresh(self, instance, active):
        """Toggle auto refresh"""
        self.auto_refresh = active
        if active:
            self.start_auto_refresh()
            self.show_snackbar("Auto refresh enabled")
        else:
            self.stop_auto_refresh()
            self.show_snackbar("Auto refresh disabled")
        self.save_settings()

    def start_auto_refresh(self):
        """Start auto refresh timer"""
        if self.refresh_event:
            self.refresh_event.cancel()
        self.refresh_event = Clock.schedule_interval(self.auto_refresh_status, 2.0)

    def stop_auto_refresh(self):
        """Stop auto refresh timer"""
        if self.refresh_event:
            self.refresh_event.cancel()
            self.refresh_event = None

    def auto_refresh_status(self, dt):
        """Auto refresh status"""
        if self.is_connected:
            self.manual_refresh()

    def manual_refresh(self, *args):
        """Manually refresh all status"""
        if not self.is_connected:
            return

        # Get PWM values
        self.send_command("GET_PWM")

        # Get stepper status
        self.send_command("GET_STEPPER")

        # Get relay status
        self.send_command("GET_RELAY")

        self.log("Status updated", "info")

    def save_arduino_settings(self, *args):
        """Save settings to Arduino EEPROM"""
        self.send_command("SAVE")
        self.show_snackbar("Settings saved to Arduino")

    def reset_arduino_settings(self, *args):
        """Reset Arduino to default settings"""
        self.send_command("RESET")
        self.show_snackbar("Arduino reset to default settings")

    # Theme Functions
    def toggle_theme(self, *args):
        """Toggle between light and dark theme"""
        if self.theme_cls.theme_style == "Dark":
            self.theme_cls.theme_style = "Light"
            self.show_snackbar("Switched to light mode")
        else:
            self.theme_cls.theme_style = "Dark"
            self.show_snackbar("Switched to dark mode")
        self.save_settings()

    def toggle_fullscreen(self, *args):
        """Toggle fullscreen mode"""
        Window.fullscreen = not Window.fullscreen
        if Window.fullscreen:
            self.show_snackbar("Fullscreen mode")
        else:
            self.show_snackbar("Window mode")

    # Settings Management
    def load_settings(self):
        """Load settings from file"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.auto_refresh = settings.get('auto_refresh', True)
                    theme_style = settings.get('theme_style', 'Dark')
                    self.theme_cls.theme_style = theme_style
        except Exception as e:
            self.log(f"Error loading settings: {str(e)}", "warning")

    def save_settings(self):
        """Save settings to file"""
        try:
            settings = {
                'auto_refresh': self.auto_refresh,
                'theme_style': self.theme_cls.theme_style
            }
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"Error saving settings: {str(e)}", "warning")

    def on_stop(self):
        """Called when app is closing"""
        self.stop_auto_refresh()

        # Stop serial communication properly
        if self.is_connected:
            self.disconnect()

        self.save_settings()
        return True

def main():
    """Main function"""
    try:
        # Set window properties
        Window.size = (400, 700)  # Mobile-friendly default size

        # Create and run app
        app = ArduinoControllerApp()
        app.run()

    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
