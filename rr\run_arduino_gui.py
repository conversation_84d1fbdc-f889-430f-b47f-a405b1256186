#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arduino Controller Launcher - Smart GUI Selector
مشغل ذكي لواجهة Arduino - يختار الواجهة المناسبة
"""

import sys
import subprocess
import os

def check_requirements():
    """Check if required packages are installed"""
    missing_packages = []
    
    try:
        import serial
    except ImportError:
        missing_packages.append("pyserial")
    
    try:
        import kivy
    except ImportError:
        missing_packages.append("kivy")
    
    try:
        import kivymd
    except ImportError:
        missing_packages.append("kivymd")
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("❌ مكتبات مطلوبة مفقودة")
        print("\nPlease install requirements:")
        print("يرجى تثبيت المتطلبات:")
        print("pip install " + " ".join(missing_packages))
        return False
    
    return True

def main():
    """Main launcher function"""
    print("🚀 Arduino Controller - Smart Launcher")
    print("🚀 واجهة Arduino - مشغل ذكي")
    print("=" * 50)
    
    # Check if GUI files exist
    gui_files = ["arduino_gui.py", "arduino_kivymd_gui.py"]
    available_guis = [f for f in gui_files if os.path.exists(f)]
    
    if not available_guis:
        print("❌ No GUI files found!")
        print("❌ لم يتم العثور على ملفات الواجهة!")
        input("Press Enter to exit...")
        return
    
    # Choose GUI
    if len(available_guis) > 1:
        print("\n📱 Available GUIs:")
        print("الواجهات المتاحة:")
        for i, gui in enumerate(available_guis, 1):
            gui_name = "KivyMD (Modern)" if "kivymd" in gui else "Tkinter (Classic)"
            print(f"{i}. {gui} - {gui_name}")
        
        try:
            choice = input("\nChoose GUI (1-2, default: KivyMD): ").strip()
            if choice == "2":
                selected_gui = "arduino_gui.py"
            else:
                selected_gui = "arduino_kivymd_gui.py" if "arduino_kivymd_gui.py" in available_guis else available_guis[0]
        except:
            selected_gui = available_guis[0]
    else:
        selected_gui = available_guis[0]
    
    print(f"\n🚀 Selected GUI: {selected_gui}")
    gui_type = "KivyMD Modern" if "kivymd" in selected_gui else "Tkinter Classic"
    print(f"🎨 Type: {gui_type}")
    
    # Check requirements
    if not check_requirements():
        print("\nInstall command:")
        print("أمر التثبيت:")
        if "kivymd" in selected_gui:
            print("pip install pyserial kivy kivymd")
        else:
            print("pip install pyserial")
        input("\nPress Enter to exit...")
        return
    
    print("✅ All requirements satisfied")
    print("✅ جميع المتطلبات متوفرة")
    print(f"\n🚀 Starting {gui_type} Arduino Controller...")
    print("🚀 بدء تشغيل واجهة Arduino العصرية...")
    print("✨ Features: Modern UI, Responsive design, Visual feedback")
    print("✨ المميزات: واجهة عصرية، تصميم متجاوب، تغذية راجعة مرئية")
    
    # Import and run the selected GUI
    try:
        if "kivymd" in selected_gui:
            import arduino_kivymd_gui
            arduino_kivymd_gui.main()
        else:
            import arduino_gui
            arduino_gui.main()
    except Exception as e:
        print(f"\n❌ Error starting GUI: {e}")
        print("❌ خطأ في تشغيل الواجهة")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ Interrupted by user")
        print("⏹️ تم الإيقاف من قبل المستخدم")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("❌ خطأ غير متوقع")
        input("Press Enter to exit...")
